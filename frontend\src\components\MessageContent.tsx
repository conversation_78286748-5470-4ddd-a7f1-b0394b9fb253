// 🎯 Objectif : Fusionner les fonctionnalités de l'ancien composant "OfferDiscussionPanel" avec le nouveau style "MessageContent"

import React, { useState, useEffect, useRef } from 'react';
import { API_BASE_URL } from './../config';
import { useNotifications } from './notifications/NotificationContext';
import { Send } from 'lucide-react';
interface User {
  id: number;
  first_name: string;
  last_name: string;
  email: string;
  email_verified_at: string;
  is_professional: boolean;
  created_at: string;
  updated_at: string;
  profile_completed: boolean;
}

interface Message {
  id: number;
  open_offer_id: number;
  sender_id: number;
  receiver_id: number;
  message_text: string;
  created_at: string;
  updated_at: string;
  sender: User;
  receiver: User;
}

// interface Message {
//   id: number;
//   sender_id: number;
//   sender_name: string;
//   sender_avatar?: string;
//   sender_type: 'client' | 'professional';
//   message_text: string;
//   attachments?: string[];
//   created_at: string;
// }

interface OfferDiscussionPanelProps {
  offerId?: number;
  offerTitle?: string;
  clientId?: number;
  clientName?: string;
  clientAvatar?: string;
  professionalId?: number;
  professionalName?: string;
  professionalAvatar?: string;
  isClient?: boolean;
  onBack?: () => void;
}
const MessageContent: React.FC<OfferDiscussionPanelProps> = ({
  offerId,
  offerTitle,
  clientId,
  clientName = "Client",  // Valeur par défaut pour éviter les erreurs
  clientAvatar,
  professionalId,
  professionalName = "Professionnel",  // Valeur par défaut pour éviter les erreurs
  professionalAvatar,
  isClient,
  onBack,
}) => {
  const [messages, setMessages] = useState<Message[]>([]);
  const [newMessage, setNewMessage] = useState('');
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [sending, setSending] = useState(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const { addOfferNotification } = useNotifications();
  const [lastMessageId, setLastMessageId] = useState<number | null>(null);

  const currentUser = JSON.parse(localStorage.getItem('user') || '{}');
  const token = localStorage.getItem('token');

  // Récupérer les messages
  // Fonction utilitaire pour récupérer les messages
  const fetchMessages = async (sinceId: number | null = null) => {
    if (!token || !offerId) return;
    setLoading(true);
    try {
      let url = `${API_BASE_URL}/api/open-offers/${offerId}/messages`;
      const params = new URLSearchParams();
      if (!isClient) {
        params.append('professional_id', String(currentUser.id));
      }
      if (sinceId !== null) {
        params.append('since_id', String(sinceId));
      }
      if (params.toString()) {
        url += `?${params.toString()}`;
      }
      const response = await fetch(url, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
      });
      if (!response.ok) {
        throw new Error('Erreur lors de la récupération des messages');
      }
      const data = await response.json();
      if (data.messages && data.messages.length > 0) {
        setMessages(data.messages); // On remplace toute la liste pour éviter les doublons/optimistes
        setLastMessageId(data.messages[data.messages.length - 1].id);
      }
    } catch (err) {
      console.error('Erreur:', err);
      setError('Impossible de charger les messages');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchMessages();
    const intervalId = setInterval(() => {
      fetchMessages(lastMessageId);
    }, 5000);
    return () => clearInterval(intervalId);
  }, [offerId, token, isClient, clientId, clientName, clientAvatar, professionalId, professionalName, professionalAvatar]);


  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [messages]);

  const handleSendMessage = async () => {
    if (!newMessage.trim()) return;
    if (!token || !offerId) return;

    // Empêcher l'envoi si aucun professionnel n'est sélectionné côté client
    if (isClient && (professionalId === undefined || professionalId === null)) {
      setError("Veuillez sélectionner un professionnel avant d'envoyer un message.");
      return;
    }

    const tempId = Date.now();
    const user = JSON.parse(localStorage.getItem('user') || '{}') as User;
    const optimisticMessage: Message = {
      id: tempId,
      open_offer_id: offerId,
      sender_id: user.id,
      receiver_id: isClient ? (professionalId || 0) : (clientId || 0),
      message_text: newMessage,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
      sender: {
        id: user.id,
        first_name: user.first_name || 'Moi',
        last_name: user.last_name || '',
        email: user.email || '',
        email_verified_at: user.email_verified_at || '',
        is_professional: user.is_professional || false,
        created_at: user.created_at || '',
        updated_at: user.updated_at || '',
        profile_completed: user.profile_completed || false,
      },
      receiver: {
        id: isClient ? (professionalId || 0) : (clientId || 0),
        first_name: '',
        last_name: '',
        email: '',
        email_verified_at: '',
        is_professional: false,
        created_at: '',
        updated_at: '',
        profile_completed: false,
      },
    };

    setMessages(prev => [...prev, optimisticMessage]);
    setNewMessage('');
    setSending(true);

    try {
      const body: any = { message_text: newMessage };
      if (isClient && professionalId !== undefined) {
        console.log('receiver_id:', professionalId);
        body.receiver_id = professionalId;
      }

      const response = await fetch(`${API_BASE_URL}/api/open-offers/${offerId}/messages`, {
        method: 'POST',
        headers: {
          Authorization: `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(body),
      });

      const data = await response.json();
      // Après l'envoi, on rafraîchit toute la liste des messages
      await fetchMessages();

      addOfferNotification('offer_message', {
        offer_id: offerId,
        offer_title: offerTitle||"",
        message: newMessage,
        ...(isClient
          ? { client_id: user.id, client_name: clientName, client_avatar: clientAvatar }
          : { professional_id: user.id, professional_name: professionalName, professional_avatar: professionalAvatar })
      });
    } catch (err) {
      console.error(err);
      setError('Envoi échoué.');
      setMessages(prev => prev.filter(m => m.id !== tempId));
    } finally {
      setSending(false);
    }
  };

  return (
    <div style={{ flex: 1, minHeight: 500, padding: 32, background: '#fff' }}>
      <div style={{ textAlign: 'center', margin: '4px 0 8px 0' }}>
        <div style={{ fontWeight: 600, fontSize: 14 }}>You sent a new project request.</div>
        <div style={{ color: '#888', fontSize: 12, marginTop: 2 }}>5:45PM</div>
      </div>
{/* 
      <div style={{ background: '#faf5ff', borderRadius: 16, border: '1px solid #eee', padding: 32, marginBottom: 24 }}>
        <div style={{ background: '#fff', borderRadius: 12, padding: 28 }}>
          <div className="mb-6">
            <div className="font-semibold mb-2">Project Overview</div>
            <div className="text-[13px] text-[#222]">Your project description here...</div>
          </div>
        </div>
      </div> */}

      <div style={{ flex: 1, overflowY: 'auto', marginBottom: 16 }}>
        {messages.map((msg) => (
          <div key={msg.id} style={{ marginBottom: 12 }}>
            <div style={{ fontWeight: 'bold', fontSize: 12 }}>{msg.sender?.first_name || 'Utilisateur'}</div>
            <div style={{ fontSize: 13, color: '#222', marginBottom: 4 }}>{msg.message_text}</div>
            <div style={{ fontSize: 11, color: '#aaa' }}>
              {msg.created_at && !isNaN(new Date(msg.created_at).getTime())
                ? new Date(msg.created_at).toLocaleTimeString()
                : ''}
            </div>
          </div>
        ))}
        <div ref={messagesEndRef} />
      </div>

      {/* Affichage de l'erreur */}
      {error && (
        <div style={{ color: 'red', marginBottom: 8 }}>
          {error}
        </div>
      )}

      <div style={{ display: 'flex', alignItems: 'center' }}>
        <div
          style={{
            display: 'flex',
            alignItems: 'center',
            background: '#fff',
            border: '1.5px solid #e5d5fa',
            borderRadius: 28,
            padding: '8px 18px',
            flex: 1,
            boxShadow: '0 1px 4px rgba(160,89,207,0.04)',
          }}
        >
          <input
            type="text"
            value={newMessage}
            onChange={(e) => setNewMessage(e.target.value)}
            onKeyDown={(e) => { if (e.key === 'Enter') handleSendMessage(); }}
            placeholder="Write your message..."
            style={{ flex: 1, border: 'none', outline: 'none', background: 'transparent', fontSize: 15 }}
          />
          <button
            onClick={handleSendMessage}
            disabled={sending || !newMessage.trim()}
            style={{ width: 36, height: 36, borderRadius: '50%', background: '#f3f0fa', border: 'none', display: 'flex', alignItems: 'center', justifyContent: 'center', cursor: 'pointer' }}
            aria-label="Send message"
          >
            <Send size={18} stroke="#a259cf" strokeWidth={2.2} />
          </button>
        </div>
      </div>
    </div>
  );
};

export default MessageContent;


// import React, { useState } from 'react';

// const MessageContent = () => {
//   return (
//     <div style={{ flex: 1, minHeight: 500, padding: 32, background: '#fff' }}>
//       {/* Bloc info envoi de projet */}
//       <div style={{ textAlign: 'center', margin: '4px 0 8px 0' }}>
//         <div style={{ fontWeight: 600, fontSize: 14 }}>You sent a new project request.</div>
//         <div style={{ color: '#888', fontSize: 12, marginTop: 2 }}>5:45PM</div>
//       </div>
//       {/* Bloc Project Request stylisé */}
//       <div style={{ background: 'rgb(250 245 255 / var(--tw-bg-opacity, 1))',  borderRadius: 16, border: '1px solid #eee', padding: 32, marginBottom: 24, boxShadow: '0 2px 8px rgba(60,30,90,0.03)' }}>
//         <div className="flex items-center mb-6">
//           {/* Icône bulle dans un cercle violet */}
//           <span className="flex items-center justify-center mr-3" style={{ width: 36, height: 36, borderRadius: '50%', background: '#a259cf1a' }}>
//             <svg width="22" height="22" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"><circle cx="12" cy="12" r="10" stroke="#a259cf" strokeWidth="2" fill="none"/><path d="M8 10h8M8 14h5" stroke="#a259cf" strokeWidth="2" strokeLinecap="round"/></svg>
//           </span>
//           <span className="font-bold text-lg text-[#222] mr-2">Project Request</span>
//           <span className="ml-1 text-gray-400 cursor-pointer" title="Info">
//             <svg width="18" height="18" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"><circle cx="12" cy="12" r="10" stroke="#bbb" strokeWidth="2" fill="none"/><text x="12" y="16" textAnchor="middle" fontSize="13" fill="#bbb" fontFamily="Arial" fontWeight="bold">i</text></svg>
//           </span>
//         </div>
//         <div style={{background: '#fff' , borderRadius: 12, padding: 28 }}>
//           <div className="mb-6">
//             <div className="font-semibold mb-2">Project Overview</div>
//             <div className="text-[13px] text-[#222]">Hi-Render is a specialized 3D visualization studio working with architects, interior designers, and developers. We are seeking a web agency or freelance web developer to redesign and develop our new website: www.hi-render.com. The goal is to create a sleek, minimal, and professional digital presence that showcases our portfolio, communicates our services clearly, and supports lead generation.</div>
//           </div>
//           <div>
//             <div className="font-semibold mb-2">Objectives</div>
//             <div className="text-[13px] text-[#222] mb-1">Reflect our brand identity: minimal, precise, architectural.</div>
//             <div className="text-[13px] text-[#222]">Improve user experience and site navigation.</div>
//           </div>
//         </div>
//       </div>
//       {/* Nouvelle barre d'envoi de message stylisée avec icônes à l'extérieur */}
//       <div style={{ display: 'flex', alignItems: 'center', marginTop: 'auto' }}>
//         {/* Icône caméra */}
//         <button
//           type="button"
//           style={{
//             width: 36,
//             height: 36,
//             borderRadius: '50%',
//             background: 'rgba(160,89,207,0.07)',
//             border: 'none',
//             display: 'flex',
//             alignItems: 'center',
//             justifyContent: 'center',
//             marginRight: 8,
//             outline: 'none',
//             cursor: 'pointer',
//           }}
//           aria-label="Send video"
//         >
//           <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="#a259cf" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"><rect x="2" y="7" width="15" height="10" rx="2"/><path d="M17 8l5 4-5 4V8z"/></svg>
//         </button>
//         {/* Icône trombone */}
//         <button
//           type="button"
//           style={{
//             width: 36,
//             height: 36,
//             borderRadius: '50%',
//             background: 'rgba(160,89,207,0.07)',
//             border: 'none',
//             display: 'flex',
//             alignItems: 'center',
//             justifyContent: 'center',
//             marginRight: 16,
//             outline: 'none',
//             cursor: 'pointer',
//           }}
//           aria-label="Attach file"
//         >
//           <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="#a259cf" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"><path d="M21.44 11.05l-9.19 9.19a5 5 0 01-7.07-7.07l9.19-9.19a3 3 0 014.24 4.24l-9.2 9.19a1 1 0 01-1.41-1.41l9.2-9.19"/></svg>
//         </button>
//         {/* Barre d'envoi (input + flèche) */}
//         <div
//           style={{
//             display: 'flex',
//             alignItems: 'center',
//             background: '#fff',
//             border: '1.5px solid #e5d5fa',
//             borderRadius: 28,
//             padding: '8px 18px',
//             flex: 1,
//             boxShadow: '0 1px 4px rgba(160,89,207,0.04)',
//           }}
//         >
//           {/* Bouton emoji */}
//           <button
//             type="button"
//             style={{
//               width: 28,
//               height: 28,
//               borderRadius: '50%',
//               background: 'none',
//               border: 'none',
//               display: 'flex',
//               alignItems: 'center',
//               justifyContent: 'center',
//               marginRight: 8,
//               cursor: 'pointer',
//               fontSize: 20,
//               color: '#a259cf',
//             }}
//             aria-label="Add emoji"
//           >
//             {/* Icône smiley SVG */}
//             <svg width="22" height="22" viewBox="0 0 24 24" fill="none" stroke="#a259cf" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
//               <circle cx="12" cy="12" r="10" />
//               <path d="M8 15s1.5 2 4 2 4-2 4-2" />
//               <line x1="9" y1="9" x2="9.01" y2="9" />
//               <line x1="15" y1="9" x2="15.01" y2="9" />
//             </svg>
//           </button>
//           <input
//             type="text"
//             placeholder="Write your message..."
//             style={{
//               flex: 1,
//               border: 'none',
//               outline: 'none',
//               background: 'transparent',
//               fontSize: 15,
//               padding: '8px 0',
//               marginRight: 12,
//               color: '#222',
//             }}
//           />
//           {/* Bouton envoyer (flèche vers le haut) */}
//           <button
//             type="button"
//             style={{
//               width: 36,
//               height: 36,
//               borderRadius: '50%',
//               background: '#f3f0fa',
//               border: 'none',
//               display: 'flex',
//               alignItems: 'center',
//               justifyContent: 'center',
//               cursor: 'pointer',
//               transition: 'background 0.2s',
//             }}
//             aria-label="Send message"
//           >
//             {/* Flèche vers le haut */}
//             <svg width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="#a259cf" strokeWidth="2.2" strokeLinecap="round" strokeLinejoin="round"><line x1="12" y1="19" x2="12" y2="5"/><polyline points="5 12 12 5 19 12"/></svg>
//           </button>
//         </div>
//       </div>
//     </div>
//   );
// };

// export default MessageContent; 