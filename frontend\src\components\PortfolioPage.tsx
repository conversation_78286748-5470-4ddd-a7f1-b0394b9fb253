import React,{ useMemo, useState } from 'react';
import CategoryList from './CategoryList';
import Gallery from './Gallery';
import { API_BASE_URL } from './../config';
import DesignersSection from '../components/DesignersSection';
import LogoServicesSection from '../components/LogoServicesSection';

interface GalleryItem {
  id: number;
  title: string;
  author: string;
  authorAvatar: string;
  isPro: boolean;
  likes: number;
  views: string;
  image: string;
}

type Props = {
  pros: any[];
  services?: any[];
  viewType?: "default" | "designers" | "services";
  query?:string;
};
const PortfolioPage: React.FC<Props> = ( {pros,viewType = "default",services,query}) => {
  const [selectedCategory, setSelectedCategory] = useState<string>('');

  const getUrlProlfil = (path : string)  => {
          return path? `${API_BASE_URL}${path}`:'https://images.unsplash.com/photo-1657921840978-c7bb981edd2b?q=80&w=1160&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D';
    };
  // Catégories statiques
  const categories = [
    'All','Modélisation 3D', 'Animation 3D', 'Rendu', 'Texturing', 'Rigging', 'Compositing', 'VFX', 'Architecture 3D', 'Jeux Vidéo'
  ];

  const handleCategorySelect = (category: string) => {
  // alert(`Filtering by category: ${category}`);
  setSelectedCategory(category);
};

  const filteredPros = useMemo(() => {
    if (!selectedCategory || selectedCategory === 'All') return pros;

    return pros.filter((pro) =>
      pro.service_offer?.some((s: any) =>
        (s.category || '').toLowerCase().includes(selectedCategory.toLowerCase())
      )
    );
  }, [selectedCategory, pros]);

  // const filteredPros = useMemo(() => {
  //   if (!selectedCategory) return pros;

  //   return pros.filter((pro) => {
  //     const inAchievements = pro.achievements?.some((a: any) =>
  //       (a.title || '').toLowerCase().includes(selectedCategory.toLowerCase())
  //     );

  //     const inServices = pro.service_offer?.some((s: any) =>
  //       (s.title || '').toLowerCase().includes(selectedCategory.toLowerCase())
  //     );

  //     return inAchievements || inServices;
  //   });
  // }, [selectedCategory, pros]);

  // const galleryItems: GalleryItem[] = useMemo(() =>
  //   pros.map((pro) => ({
  //     id: pro.id,
  //     title: pro.title || "Titre manquant",
  //     author: `${pro.first_name} ${pro.last_name}`,
  //     authorAvatar: getUrlProlfil(String(pro.profile_picture_path)),
  //     isPro: pro.availability_status === 'available',
  //     likes: (pro.achievements?.length || 0) * 10,
  //     views: `${((pro.review_count || 0) * 2.5).toFixed(1)}k`,
  //     // image: pro.achievements?.[0]?.image_url || "https://via.placeholder.com/400x300.png?text=No+Image",
  //     image:
  //       pro.achievements?.[0]?.image_url ||
  //       pro.service_offer?.[0]?.image_url ||
  //       `${API_BASE_URL}${pro.profile_picture_path}` ||
  //       "https://via.placeholder.com/400x300.png?text=No+Image"
  //   }))
  // , [pros]);

  const galleryItems: GalleryItem[] = useMemo(() =>
    filteredPros.map((pro) => ({
      id: pro.id,
      title: pro.title || "Untitled",
      author: `${pro.first_name} ${pro.last_name}`,
      authorAvatar: getUrlProlfil(pro.profile_picture_path || ""),
      isPro: pro.availability_status === 'available',
      likes: (pro.achievements?.length || 0) * 10,
      views: `${((pro.review_count || 0) * 2.5).toFixed(1)}k`,
      image:
        pro.achievements?.[0]?.image_url ||
        pro.service_offer?.[0]?.image_url ||
        `${API_BASE_URL}${pro.profile_picture_path}` ||
        "https://via.placeholder.com/400x300.png?text=No+Image"
    }))
  , [filteredPros]);

  return (
    <div style={{ marginTop: '60px' }}>
      <CategoryList 
        categories={categories}
        onCategorySelect={handleCategorySelect}
        selectedCategory={selectedCategory}
      />
      {/* <Gallery items={galleryItems} /> */}
      {viewType === "default" && <Gallery items={galleryItems} />}
      {viewType === "designers" && 
        <DesignersSection 
          professionals={filteredPros}
          query={query}
        />
      }
      {viewType === "services" && 
        <LogoServicesSection 
          services={services} 
          query={query} 
        />
      }
    </div>
  );
};

export default PortfolioPage; 