import React, { useState } from 'react';
import { useNavigate, Link } from 'react-router-dom';
import { API_BASE_URL } from './../config';

type GalleryItem = {
  id: number;
  title: string;
  author: string;
  authorAvatar: string;
  isPro?: boolean;
  likes: number;
  views: string;
  image: string;
};

type Props = {
  items: GalleryItem[];
  marginBottom?: number | string;
};

const Gallery: React.FC<Props> = ({ items, marginBottom }) => {
  const navigate = useNavigate();
  const [likedProfiles, setLikedProfiles] = useState<number[]>([]);
  const [likesCount, setLikesCount] = useState<{ [key: number]: number }>(
    items.reduce((acc, item) => ({ ...acc, [item.id]: item.likes }), {})
  );

  const handleLike = async (e: React.MouseEvent, professionalProfileId: number) => {
    e.stopPropagation();

    const token = localStorage.getItem("token");

    if (!token) {
      navigate("/login");
      return;
    }

    try {
      const response = await fetch(
        `${API_BASE_URL}/api/professionals/${professionalProfileId}/like`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            "Authorization": `Bearer ${token}`,
          },
        }
      );


      if (!response.ok) throw new Error("Erreur lors du like");

      
      const data = await response.json();
      console.log("Reponse : ",data)

      if (data.success) {
        const { liked, total_likes } = data.data;

        // ✅ Met à jour les profils likés
        setLikedProfiles(prev =>
          liked
            ? [...prev, professionalProfileId]
            : prev.filter(id => id !== professionalProfileId)
        );

        // ✅ Met à jour le compteur de likes
        setLikesCount(prev => ({
          ...prev,
          [professionalProfileId]: total_likes
        }));
      }
      } catch (err) {
        console.error("Erreur like :", err);
      }
    };

  return (
    <div
      className="max-w-[1357px] mx-auto"
      style={marginBottom !== undefined ? { marginBottom } : undefined}
    >
      <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6 sm:gap-8 px-2 sm:px-0 justify-items-center">
        {items.map(item => {
          const isLiked = likedProfiles.includes(item.id);
          const totalLikes = likesCount[item.id] || item.likes;

          return (
            <div
              key={item.id}
              className="group w-[90vw] max-w-[315px] bg-white rounded-lg overflow-hidden m-auto relative shadow-none flex flex-col cursor-pointer transition-transform duration-300 hover:scale-[1.01]"
              onClick={() => navigate(`/professionals/${item.id}`)}
            >
              <div
                style={{ backgroundImage: `url(${item.image})` }}
                className="bg-[#2d241b] bg-cover bg-center w-full h-[220px] rounded-lg"
              />

              {/* Coeur flottant animé au survol */}
              <button
                onClick={(e) => handleLike(e, item.id)}
                className="absolute top-3 right-3 bg-white/80 backdrop-blur-sm rounded-full p-2 transition duration-300 opacity-0 group-hover:opacity-100 hover:scale-110 z-10"
                aria-label="Liker ce profil"
              >
                <svg
                  width="20"
                  height="20"
                  viewBox="0 0 24 24"
                  fill={isLiked ? "#ff4d6d" : "#b3b3b3"}
                >
                  <path d="M12 21.35l-1.45-1.32C5.4 15.36 
                    2 12.28 2 8.5 2 5.42 4.42 3 7.5 3c1.74 
                    0 3.41 0.81 4.5 2.09C13.09 3.81 14.76 
                    3 16.5 3 19.58 3 22 5.42 22 8.5c0 
                    3.78-3.4 6.86-8.55 11.54L12 21.35z"/>
                </svg>
              </button>

              <div className="w-full flex justify-between items-center px-0 pt-[18px] pb-2.5 min-h-[60px] text-[12px]" style={{ fontFamily: 'Arial, sans-serif' }}>
                <div className="flex items-center gap-2.5">
                  <img
                    src={item.authorAvatar}
                    alt={item.author}
                    className="w-8 h-8 rounded-full object-cover border-2 border-[#eee]"
                  />
                  <span className="font-semibold truncate max-w-[90px]">{item.author}</span>
                  {item.isPro && (
                    <span className="bg-[#f3f3f3] text-[#888] font-bold rounded px-2 py-0.5 ml-1">PRO</span>
                  )}
                </div>
                <div className="flex items-center gap-4 text-[#888]">
                  {/* ❤️ cœur en bas  (réagit aussi au like) */}
                  <span className="flex items-center gap-1">
                    <svg width="18" height="18" viewBox="0 0 24 24" fill={isLiked ? "#ff4d6d" : "#b3b3b3"}>
                      <path d="M12 21.35l-1.45-1.32C5.4 15.36 
                        2 12.28 2 8.5 2 5.42 4.42 3 7.5 3c1.74 
                        0 3.41 0.81 4.5 2.09C13.09 3.81 14.76 
                        3 16.5 3 19.58 3 22 5.42 22 8.5c0 
                        3.78-3.4 6.86-8.55 11.54L12 21.35z"/>
                    </svg>
                    {totalLikes}
                  </span>

                  {/* 👁️ vues */}
                  <span className="flex items-center gap-1">
                    <svg width="18" height="18" viewBox="0 0 24 24" fill="#b3b3b3">
                      <path d="M12 5c-7 0-10 7-10 7s3 7 10 7 
                        10-7 10-7-3-7-10-7zm0 12c-2.76 
                        0-5-2.24-5-5s2.24-5 5-5 5 2.24 
                        5 5-2.24 5-5 5zm0-8a3 3 0 100 6 
                        3 3 0 000-6z"/>
                    </svg>
                    {item.views}
                  </span>
                </div>
              </div>
            </div>
          );
        })}
      </div>
    </div>
  );
};

export default Gallery;


//---------------------------------------------------------------------------------------------------------
// import React from 'react';
// import { useNavigate, Link } from 'react-router-dom';
// import { API_BASE_URL } from './../config';

// type GalleryItem = {
//   id: number;
//   title: string;
//   author: string;
//   authorAvatar: string;
//   isPro?: boolean;
//   likes: number;
//   views: string;
//   image: string;
// };

// type Props = {
//   items: GalleryItem[];
//   marginBottom?: number | string;
// };

// const Gallery: React.FC<Props> = ({ items, marginBottom }) => {
//   const navigate = useNavigate();

//   const handleLike = async (e: React.MouseEvent, professionalProfileId: number) => {
//     e.stopPropagation(); // empêche la navigation lors du clic sur le bouton like

    
//     const token = localStorage.getItem('token');
//     const user = JSON.parse(localStorage.getItem('user') || '{}');

//     if (!token) {
//       navigate("/login");
//       return;
//     }

//     try {
//       const response = await fetch(
//         `${API_BASE_URL}/professionals/${professionalProfileId}/like`,
//         {
//           method: "POST",
//           headers: {
//             "Content-Type": "application/json",
//             "Authorization": `Bearer ${token}`,
//           },
//         }
//       );

//       if (!response.ok) {
//         throw new Error("Erreur lors du like du profil");
//       }

//       const data = await response.json();
//       console.log("Profil liké :", data);
//       // Tu peux déclencher ici une mise à jour du state local pour afficher un cœur rouge si besoin
//     } catch (error) {
//       console.error("Erreur like :", error);
//     }
//   };

//   if (items.length === 0) {
//     return (
//       <div className="text-center py-20 text-gray-600 flex flex-col items-center justify-center">
//         <svg
//           xmlns="http://www.w3.org/2000/svg"
//           className="w-24 h-24 text-gray-400 mb-6"
//           fill="none"
//           viewBox="0 0 24 24"
//           stroke="currentColor"
//           strokeWidth={1.5}
//         >
//           <path
//             strokeLinecap="round"
//             strokeLinejoin="round"
//             d="M3 7a2 2 0 012-2h5l2 2h7a2 2 0 012 2v7a2 2 0 01-2 2H5a2 2 0 01-2-2V7z"
//           />
//         </svg>
//         <h2 className="text-2xl font-semibold">No professionals found</h2>
//         <p className="text-gray-500 mt-2 max-w-md">
//           We couldn’t find any professionals matching your search or category. Try again with a different filter.
//         </p>
//       </div>
//     );
//   }

//   return (
//     <div
//       className="max-w-[1357px] mx-auto"
//       style={marginBottom !== undefined ? { marginBottom } : undefined}
//     >
//       <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6 sm:gap-8 px-2 sm:px-0 justify-items-center">
//         {items.map(item => (
//           <div
//             key={item.id}
//             className="group w-[90vw] max-w-[315px] bg-white rounded-lg overflow-hidden m-auto relative shadow-none flex flex-col cursor-pointer transition-transform duration-300 hover:scale-[1.01]"
//             onClick={() => navigate(`/professionals/${item.id}`)}
//           >
//             <div
//               style={{
//                 backgroundImage: `url(${item.image})`,
//               }}
//               className="bg-[#2d241b] bg-cover bg-center w-full h-[220px] rounded-lg"
//               aria-label={item.title}
//               role="img"
//             />

//             {/* Bouton like animé */}
//             <button
//               onClick={(e) => handleLike(e, item.id)}
//               className="absolute top-3 right-3 bg-white/80 backdrop-blur-sm rounded-full p-2 transition duration-300 opacity-0 group-hover:opacity-100 hover:scale-110 z-10"
//               aria-label="Liker ce profil"
//             >
//               <svg
//                 width="20"
//                 height="20"
//                 viewBox="0 0 24 24"
//                 fill="#ff4d6d"
//               >
//                 <path d="M12 21.35l-1.45-1.32C5.4 15.36 2 12.28 2 8.5 
//                          2 5.42 4.42 3 7.5 3c1.74 0 3.41 0.81 
//                          4.5 2.09C13.09 3.81 14.76 3 16.5 3 
//                          19.58 3 22 5.42 22 8.5c0 3.78-3.4 
//                          6.86-8.55 11.54L12 21.35z"/>
//               </svg>
//             </button>

//             <div
//               className="w-full flex justify-between items-center px-0 pt-[18px] pb-2.5 min-h-[60px] text-[12px]"
//               style={{ fontFamily: 'Arial, sans-serif' }}
//             >
//               <div className="flex items-center gap-2.5">
//                 <img
//                   src={item.authorAvatar}
//                   alt={item.author}
//                   className="w-8 h-8 rounded-full object-cover border-2 border-[#eee]"
//                 />
//                 <span className="font-semibold truncate max-w-[90px]">{item.author}</span>
//                 {item.isPro && (
//                   <span className="bg-[#f3f3f3] text-[#888] font-bold rounded px-2 py-0.5 ml-1">PRO</span>
//                 )}
//               </div>
//               <div className="flex items-center gap-4 text-[#888]">
//                 <span className="flex items-center gap-1">
//                   <svg width="18" height="18" viewBox="0 0 24 24" fill="#b3b3b3" className="mr-0.5">
//                     <path d="M12 21.35l-1.45-1.32C5.4 15.36 
//                       2 12.28 2 8.5 2 5.42 4.42 3 7.5 3c1.74 
//                       0 3.41 0.81 4.5 2.09C13.09 3.81 14.76 
//                       3 16.5 3 19.58 3 22 5.42 22 8.5c0 
//                       3.78-3.4 6.86-8.55 11.54L12 21.35z"/>
//                   </svg>
//                   {item.likes}
//                 </span>
//                 <span className="flex items-center gap-1">
//                   <svg width="18" height="18" viewBox="0 0 24 24" fill="#b3b3b3" className="mr-0.5">
//                     <path d="M12 5c-7 0-10 7-10 7s3 7 10 7 
//                       10-7 10-7-3-7-10-7zm0 12c-2.76 
//                       0-5-2.24-5-5s2.24-5 5-5 5 2.24 
//                       5 5-2.24 5-5 5zm0-8a3 3 0 100 6 
//                       3 3 0 000-6z"/>
//                   </svg>
//                   {item.views}
//                 </span>
//               </div>
//             </div>
//           </div>
//         ))}
//       </div>
//     </div>
//   );
// };

// export default Gallery;

//------------------------------------------------------------------------------------------------

// import React from 'react';
// import { useNavigate } from 'react-router-dom';
// import { Link } from 'react-router-dom';
// import { API_BASE_URL } from './../config';

// type GalleryItem = {
//   id: number;
//   title: string;
//   author: string;
//   authorAvatar: string;
//   isPro?: boolean;
//   likes: number;
//   views: string;
//   image: string;
// };

// type Props = {
//   items: GalleryItem[];
//   marginBottom?: number | string;
// };

// const Gallery: React.FC<Props> = ({ items, marginBottom }) => {
//    const navigate = useNavigate();

//     if (items.length === 0) {
//     return (
//       <div className="text-center py-20 text-gray-600 flex flex-col items-center justify-center">
//         <svg
//           xmlns="http://www.w3.org/2000/svg"
//           className="w-24 h-24 text-gray-400 mb-6"
//           fill="none"
//           viewBox="0 0 24 24"
//           stroke="currentColor"
//           strokeWidth={1.5}
//         >
//           <path
//             strokeLinecap="round"
//             strokeLinejoin="round"
//             d="M3 7a2 2 0 012-2h5l2 2h7a2 2 0 012 2v7a2 2 0 01-2 2H5a2 2 0 01-2-2V7z"
//           />
//         </svg>
//         <h2 className="text-2xl font-semibold">No professionals found</h2>
//         <p className="text-gray-500 mt-2 max-w-md">
//           We couldn’t find any professionals matching your search or category. Try again with a different filter.
//         </p>
//       </div>
//     );
//   }

//   return (
//     <div
//       className="max-w-[1357px] mx-auto"
//       style={marginBottom !== undefined ? { marginBottom } : undefined}
//     >
//       <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6 sm:gap-8 px-2 sm:px-0 justify-items-center">
//         {items.map(item => (
//           <div
//             key={item.id}
//             className="w-[90vw] max-w-[315px] bg-white rounded-lg overflow-hidden m-auto relative shadow-none flex flex-col"
//             onClick={() => navigate(`/professionals/${item.id}`)}
//           >
//             <div
//               style={{
//                 backgroundImage: `url(${item.image})`,
//               }}
//               className="bg-[#2d241b] bg-cover bg-center w-full h-[220px] rounded-lg"
//               aria-label={item.title}
//               role="img"
//             />
//             <div
//               className="w-full flex justify-between items-center px-0 pt-[18px] pb-2.5 min-h-[60px] text-[12px]" style={{ fontFamily: 'Arial, sans-serif' }}
//             >
//               <div className="flex items-center gap-2.5">
//                 <img
//                   src={item.authorAvatar}
//                   alt={item.author}
//                   className="w-8 h-8 rounded-full object-cover border-2 border-[#eee]"
//                 />
//                 <span className="font-semibold truncate max-w-[90px]">{item.author}</span>
//                 {item.isPro && (
//                   <span className="bg-[#f3f3f3] text-[#888] font-bold rounded px-2 py-0.5 ml-1">PRO</span>
//                 )}
//               </div>
//               <div className="flex items-center gap-4 text-[#888]">
//                 <span className="flex items-center gap-1">
//                   <Link to="/favorite" className="flex items-center gap-1 hover:opacity-80" aria-label="Voir les favoris">
//                     <svg width="18" height="18" viewBox="0 0 24 24" fill="#b3b3b3" className="mr-0.5"><path d="M12 21.35l-1.45-1.32C5.4 15.36 2 12.28 2 8.5 2 5.42 4.42 3 7.5 3c1.74 0 3.41 0.81 4.5 2.09C13.09 3.81 14.76 3 16.5 3 19.58 3 22 5.42 22 8.5c0 3.78-3.4 6.86-8.55 11.54L12 21.35z"/></svg>
//                   </Link>
//                   {item.likes}
//                 </span>
//                 <span className="flex items-center gap-1">
//                   <svg width="18" height="18" viewBox="0 0 24 24" fill="#b3b3b3" className="mr-0.5"><path d="M12 5c-7 0-10 7-10 7s3 7 10 7 10-7 10-7-3-7-10-7zm0 12c-2.76 0-5-2.24-5-5s2.24-5 5-5 5 2.24 5 5-2.24 5-5 5zm0-8a3 3 0 100 6 3 3 0 000-6z"/></svg>
//                   {item.views}
//                 </span>
//               </div>
//             </div>
//           </div>
//         ))}
//       </div>
//     </div>
//   );
// };

// export default Gallery; 